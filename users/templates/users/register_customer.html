{% extends 'main/base.html' %}
{% block title %}
  Customer Registration
{% endblock %}

{% block content %}
    <div class="registration-container">
        <h1>Register as Customer</h1>
        <p class="registration-subtitle">Join <PERSON>F<PERSON> to request home services</p>

        <form method="POST" class="registration-form">
            {% csrf_token %}
            {% if request.GET.next %}
                <input type="hidden" name="next" value="{{ request.GET.next }}">
            {% endif %}

            {% if form.non_field_errors %}
                <div class="error-message">
                    {{ form.non_field_errors }}
                </div>
            {% endif %}

            <div class="form-group">
                <label for="{{ form.username.id_for_label }}">Username:</label>
                {{ form.username }}
                {% if form.username.errors %}
                    <div class="error">{{ form.username.errors }}</div>
                {% endif %}
            </div>

            <div class="form-group">
                <label for="{{ form.email.id_for_label }}">Email:</label>
                {{ form.email }}
                {% if form.email.errors %}
                    <div class="error">{{ form.email.errors }}</div>
                {% endif %}
            </div>

            <div class="form-group">
                <label for="{{ form.date_of_birth.id_for_label }}">Date of Birth:</label>
                {{ form.date_of_birth }}
                {% if form.date_of_birth.errors %}
                    <div class="error">{{ form.date_of_birth.errors }}</div>
                {% endif %}
            </div>

            <div class="form-group">
                <label for="{{ form.password1.id_for_label }}">Password:</label>
                {{ form.password1 }}
                {% if form.password1.errors %}
                    <div class="error">{{ form.password1.errors }}</div>
                {% endif %}
            </div>

            <div class="form-group">
                <label for="{{ form.password2.id_for_label }}">Confirm Password:</label>
                {{ form.password2 }}
                {% if form.password2.errors %}
                    <div class="error">{{ form.password2.errors }}</div>
                {% endif %}
            </div>

            <div class="form-actions">
                <button type="submit" class="btn btn-primary">Register as Customer</button>
                <a href="/register/" class="btn btn-secondary">Back</a>
            </div>
        </form>

        <div class="login-link">
            <p>Already have an account? <a href="/register/login/{% if request.GET.next %}?next={{ request.GET.next|urlencode }}{% endif %}">Login here</a></p>
        </div>
    </div>
{% endblock %}