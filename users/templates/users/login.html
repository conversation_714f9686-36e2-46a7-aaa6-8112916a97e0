{% extends 'main/base.html' %}
{% block title %}
    Login - NetFix
{% endblock %}

{% block content %}
    <div class="registration-container">
        <h1>Welcome Back</h1>
        <p class="registration-subtitle">Sign in to your NetFix account</p>

        <form method="post" class="registration-form">
            {% csrf_token %}
            {% if request.GET.next %}
                <input type="hidden" name="next" value="{{ request.GET.next }}">
            {% endif %}

            {% if form.non_field_errors %}
                <div class="error-message">
                    {{ form.non_field_errors }}
                </div>
            {% endif %}

            <div class="form-group">
                <label for="{{ form.email.id_for_label }}">Email Address:</label>
                {{ form.email }}
                {% if form.email.errors %}
                    <div class="error">{{ form.email.errors }}</div>
                {% endif %}
            </div>

            <div class="form-group">
                <label for="{{ form.password.id_for_label }}">Password:</label>
                {{ form.password }}
                {% if form.password.errors %}
                    <div class="error">{{ form.password.errors }}</div>
                {% endif %}
            </div>

            <div class="form-actions">
                <button type="submit" class="btn btn-primary">Sign In</button>
            </div>
        </form>

        <div class="login-link">
            <p>Don't have an account? <a href="/register/">Sign up here</a></p>
        </div>
    </div>
{% endblock %}