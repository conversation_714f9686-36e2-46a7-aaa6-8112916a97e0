{% extends 'main/base.html' %}
{% block title %}
    Create New Service
{% endblock %}

{% block content %}
    <div class="create-service-container">
        <h1>Create New Service</h1>

        {% if user.is_company %}
            <div class="company-info-banner">
                <h3>Company: {{ company.user.username }}</h3>
                <p><strong>Specialization:</strong> {{ company.field_of_work }}</p>
                {% if company.field_of_work == "All in One" %}
                    <p class="info-text">As an "All in One" company, you can create services in any category.</p>
                {% else %}
                    <p class="info-text">You can only create services in the {{ company.field_of_work }} category.</p>
                {% endif %}
            </div>

            <form method="post">
                {% csrf_token %}
                
                <div class="form-group">
                    <label for="{{ form.name.id_for_label }}">Service Name:</label>
                    {{ form.name }}
                    {% if form.name.errors %}
                        <div class="error">{{ form.name.errors }}</div>
                    {% endif %}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.description.id_for_label }}">Description:</label>
                    {{ form.description }}
                    {% if form.description.errors %}
                        <div class="error">{{ form.description.errors }}</div>
                    {% endif %}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.price_hour.id_for_label }}">Price per Hour ($):</label>
                    {{ form.price_hour }}
                    {% if form.price_hour.errors %}
                        <div class="error">{{ form.price_hour.errors }}</div>
                    {% endif %}
                </div>
                
                <div class="form-group">
                    <label for="{{ form.field.id_for_label }}">Service Category:</label>
                    {{ form.field }}
                    {% if form.field.help_text %}
                        <small class="help-text">{{ form.field.help_text }}</small>
                    {% endif %}
                    {% if form.field.errors %}
                        <div class="error">{{ form.field.errors }}</div>
                    {% endif %}
                </div>
                
                {% if form.non_field_errors %}
                    <div class="error">{{ form.non_field_errors }}</div>
                {% endif %}
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">Create Service</button>
                    <a href="/services/" class="btn btn-secondary">Cancel</a>
                </div>
            </form>
        {% else %}
            <div class="error-message">
                <p>Only companies can create services. Please register as a company to create services.</p>
                <a href="/register/" class="btn btn-primary">Register as Company</a>
            </div>
        {% endif %}
    </div>
{% endblock %}
