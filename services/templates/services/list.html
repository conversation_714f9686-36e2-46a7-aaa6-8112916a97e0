{% extends 'main/base.html' %}
{% block title %}
    All Services - NetFix
{% endblock %}

{% block content %}
    <div class="services-container">
        <div class="services-header">
            <h1>All Services</h1>
            <p class="services-subtitle">Browse all available services (newest first)</p>
        </div>

        <div class="services-navigation">
            <div class="nav-links">
                <a href="/services/" class="nav-link active">All Services</a>
                <a href="/services/most-requested/" class="nav-link">Most Requested</a>
                {% if user.is_company %}
                    <a href="/services/create/" class="nav-link">Create Service</a>
                {% endif %}
            </div>
        </div>

        {% if services %}
            <div class="services-grid">
                {% for service in services %}
                    <div class="service-card">
                        <div class="service-header">
                            <h3><a href="/services/{{ service.id }}">{{ service.name }}</a></h3>
                            <span class="new-badge">
                                {% if service.date_created|timesince < "7 days" %}
                                    ✨ New
                                {% endif %}
                            </span>
                        </div>

                        <div class="service-info">
                            <p><strong>Company:</strong> <a href="/company/{{ service.company.user.username }}">{{ service.company.user.username }}</a></p>
                            <p><strong>Category:</strong> <span class="category-tag">{{ service.field }}</span></p>
                            <p><strong>Price per Hour:</strong> <span class="price">${{ service.price_hour }}</span></p>
                            <p><strong>Description:</strong> {{ service.description|truncatewords:15 }}</p>
                            <p><strong>Created:</strong> {{ service.date_created|date:"M d, Y" }}</p>
                        </div>

                        <div class="service-actions">
                            <a href="/services/{{ service.id }}" class="btn btn-primary">View Details</a>
                            {% if user.is_authenticated and user.is_customer %}
                                <a href="/services/{{ service.id }}/request_service/" class="btn btn-secondary">Request Service</a>
                            {% endif %}
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="no-services">
                <h3>No Services Available</h3>
                <p>There are currently no services available. Be the first to create one!</p>
                {% if user.is_authenticated and user.is_company %}
                    <a href="/services/create/" class="btn btn-primary">Create First Service</a>
                {% else %}
                    <a href="/register/" class="btn btn-primary">Register as Company</a>
                {% endif %}
            </div>
        {% endif %}

        <div class="services-footer">
            <div class="category-links">
                <h4>Browse by Category</h4>
                <div class="category-grid">
                    <a href="/services/air-conditioner/" class="category-link">Air Conditioner</a>
                    <a href="/services/carpentry/" class="category-link">Carpentry</a>
                    <a href="/services/electricity/" class="category-link">Electricity</a>
                    <a href="/services/gardening/" class="category-link">Gardening</a>
                    <a href="/services/home-machines/" class="category-link">Home Machines</a>
                    <a href="/services/housekeeping/" class="category-link">Housekeeping</a>
                    <a href="/services/interior-design/" class="category-link">Interior Design</a>
                    <a href="/services/locks/" class="category-link">Locks</a>
                    <a href="/services/painting/" class="category-link">Painting</a>
                    <a href="/services/plumbing/" class="category-link">Plumbing</a>
                    <a href="/services/water-heaters/" class="category-link">Water Heaters</a>
                </div>
            </div>
        </div>
    </div>
{% endblock %}
